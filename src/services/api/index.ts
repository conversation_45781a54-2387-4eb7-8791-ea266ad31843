/**
 * Main API configuration using RTK Query
 */
import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'

import { ENDPOINTS_WITHOUT_REFRESH } from './base-query'
import { createAuthEndpoints } from './endpoints/auth.api'
import { createDownloadEndpoints } from './endpoints/download.api'
import { createNotifyEndpoints } from './endpoints/notify.api'
import { createProtocolEndpoints } from './endpoints/protocol.api'
import { createRemoteEndpoints } from './endpoints/remote.api'
import { createStructureEndpoints } from './endpoints/structure.api'
import { createWorklistEndpoints } from './endpoints/worklist.api'
import type { RootState } from '../../store/index'

/**
 * Base query with authentication and token refresh logic
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const baseQueryWithReauth = (baseQuery: any) => async (args: any, api: any, extraOptions: any) => {
  const result = await baseQuery(args, api, extraOptions)

  // Temporarily simplified to avoid circular dependency
  // Handle 401 errors
  if (result.error && result.error.status === 401) {
    // eslint-disable-next-line no-console
    console.warn('Authentication failed - 401 error')
    // TODO: Implement proper auth handling without circular dependency
    return result
  }

  // Skip token refresh for certain endpoints
  if (ENDPOINTS_WITHOUT_REFRESH.includes(api.endpoint)) {
    return result
  }

  // Temporarily disabled token refresh to avoid circular dependency
  // TODO: Re-implement token refresh without circular dependency

  return result
}

export const api = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth(fetchBaseQuery({
    baseUrl: window.config.env.api.rest,
    prepareHeaders: (headers, { getState }) => {
      const { token } = (getState() as RootState).authReducer

      if (token) {
        headers.set('authorization', `Bearer ${token}`)
      }

      // Security headers
      headers.set('X-Frame-Options', 'DENY')
      headers.set('Content-Security-Policy', 'frame-ancestors \'none\'')

      return headers
    },
  })),
  endpoints: (builder) => ({
    ...createAuthEndpoints(builder), // Authentication endpoints
    ...createNotifyEndpoints(builder), // Notification endpoints
    ...createWorklistEndpoints(builder), // Worklist endpoints
    ...createProtocolEndpoints(builder), // Protocol endpoints
    ...createStructureEndpoints(builder), // Structure endpoints
    ...createRemoteEndpoints(builder), // Remote endpoints
    ...createDownloadEndpoints(builder), // Download endpoints
  }),
})

// Export hooks for components
export const {
  // Auth
  usePostLoginMutation,
  useDeleteLoginMutation,

  // Notify
  useGetNotifyMutation,
  useReGetNotifyMutation,
  useDeleteNotifyMutation,

  // Worklist
  useGetWorklistGroupMutation,
  useReGetWorklistGroupMutation,
  usePatchPrioritizeWorklistGroupMutation,
  usePatchWorklistGroupMutation,
  useGetWorklistMutation,
  useReGetWorklistMutation,
  usePatchWorklistStatusMutation,
  useGetWorklistDetailMutation,
  usePutWorklistMutation,
  usePostWorklistRedrawMutation,
  usePostRsResendMutation,
  useGetRsPreviewZipMutation,

  // Protocol
  useLazyGetProtocolQuery,
  useGetProtocolConfigMutation,
  useGetProtocolDetailMutation,
  useGetWorklistProtocolDetailMutation,
  useCheckProtocolNameMutation,
  usePostProtocolMutation,
  usePutProtocolMutation,
  useDeleteProtocolMutation,
  usePutProtocolSortMutation,
  usePatchProtocolStatusMutation,

  // Structure
  useGetStructureMutation,
  usePutStructureMutation,
  useGetStructureConfigMutation,

  // Remote
  useGetRemoteMutation,
  useGetRemoteStatusMutation,
  useGetRemoteConfigMutation,
  usePutRemoteMutation,
  usePostRemoteServerMutation,
  useDeleteRemoteServerMutation,
  usePostFolderMutation,
  useDeleteFolderMutation,

  // Download
  useGetDicomMutation,
} = api

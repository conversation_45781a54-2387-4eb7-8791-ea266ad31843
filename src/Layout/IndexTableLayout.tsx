import { Col, Row } from 'antd'
import type { FormInstance } from 'antd/es/form'
import { Content } from 'antd/es/layout/layout'
import type { SorterResult } from 'antd/es/table/interface'

import { Head } from 'src/components/Head'
import { SearchList } from 'src/components/List'
import HistoryWorklistTable from 'src/components/Table/HistoryWorklistTable'
import WorklistGroupTable from 'src/components/Table/WorklistGroupTable'
import WorklistTable from 'src/components/Table/WorklistTable'
import i18n from 'src/i18n'
import type { SorterInfo } from 'src/pages/WorklistPage/types'

interface IndexTableLayoutProps {
  // Page state
  isHistoryPage: boolean
  pageTitle: string
  searchOptions: OptionType[]

  // Form
  form: FormInstance

  // Loading states
  groupTableLoading: boolean
  tableLoading: boolean

  // Sorting states
  groupSorted: SorterResult<WorklistGroupType>
  worklistSorted: SorterResult<SeriesType>
  setWorklistSorted: (sorted: SorterResult<SeriesType>) => void
  setHistorySorted?: (sorted: SorterResult<HistoryWorklistType>) => void

  // Handlers
  handleSearch: (page?: number, sorter?: SorterInfo) => void
}

/**
 * Pure UI Layout component for table-based pages (Worklist/History)
 */
function IndexTableLayout({
  isHistoryPage,
  pageTitle,
  searchOptions,
  form,
  groupTableLoading,
  tableLoading,
  groupSorted,
  worklistSorted,
  setWorklistSorted,
  setHistorySorted,
  handleSearch,
}: IndexTableLayoutProps) {
  return (
    <>
      <Head>{i18n.t(pageTitle)}</Head>
      <Content
        style={{
          height: 'calc(100% - 120px)',
          overflow: 'auto',
          padding: 0,
        }}
      >
        <Row>
          <SearchList
            form={form}
            optionList={searchOptions}
            handleSearch={handleSearch}
            datePicker={isHistoryPage}
          />
        </Row>
        <Row style={{ height: '100%', overflow: 'auto' }}>
          <Col span={8}>
            <WorklistGroupTable
              tableLoading={groupTableLoading}
              history={isHistoryPage}
              sortedInfo={groupSorted}
              handleDataChange={handleSearch}
            />
          </Col>
          <Col span={16} style={{ height: '100%' }}>
            {isHistoryPage ? (
              <HistoryWorklistTable
                tableLoading={tableLoading}
                setSortedInfo={setHistorySorted || (() => { })}
              />
            ) : (
              <WorklistTable
                tableLoading={tableLoading}
                sortedInfo={worklistSorted}
                setSortedInfo={setWorklistSorted}
              />
            )}
          </Col>
        </Row>
      </Content>
    </>
  )
}

export default IndexTableLayout

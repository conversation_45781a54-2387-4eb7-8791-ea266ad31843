import React from 'react'

import {
  ConfigProvider, Table,
} from 'antd'
import type { SorterResult } from 'antd/es/table/interface'

import { LoadingIcon2 } from 'src/assets/icons'
import { historyWorklistMockData } from 'src/components/mock/history-worklist.mock'
import { useAppSelector } from 'src/store/hook'
import { color } from 'src/styles/utils/variables'

import './HistoryWorklistTable.css'

import ExpandIcon from './components/ExpandIcon'
import { useHistoryTreeTableColumns } from './hooks/useTableColumns'
import { useHistoryTreeTableData } from './hooks/useTableData'

// 定義樹形數據類型
interface TreeDataType {
  key: React.ReactNode
  list_id?: number
  group_id?: number
  study_date?: string
  study_description?: string
  modality?: ModalityType | ModalityType[]
  images?: number
  series_number?: number
  series_description?: string
  source?: string
  status?: string
  level: 'main' | 'group' | 'item'
  children?: TreeDataType[]
}

interface Props {
  tableLoading: boolean
  setSortedInfo: (sorter: SorterResult<HistoryWorklistType>) => void
}

/**
 * History version of WorklistTable with Antd tree table structure
 */
function HistoryWorklistTable({
  tableLoading, setSortedInfo,
}: Props) {
  const {
    worklistGroup, worklistGroupFocus,
  } = useAppSelector((state) => state.worklistReducer)

  const worklistGroupFocusInfo = worklistGroup?.filter(({ id }) => id === worklistGroupFocus.id)[0]

  // 使用自定義 hooks
  const { treeColumns } = useHistoryTreeTableColumns(worklistGroupFocusInfo)
  const { treeData } = useHistoryTreeTableData(historyWorklistMockData)

  return (
    <ConfigProvider
      theme={{
        components: {
          Table: {
            cellPaddingBlock: 16,
            cellPaddingInline: 8,
            headerBg: color.gray[3],
          },
        },
      }}
    >
      <section
        style={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          padding: '.75rem 1rem',
          background: color.gray[3],
        }}
      >
        <Table<TreeDataType>
          id="history-worklist-table"
          columns={treeColumns}
          dataSource={treeData}
          loading={{
            spinning: tableLoading,
            delay: 500,
            indicator: <LoadingIcon2 className="spin-animation" />,
          }}
          pagination={false}
          scroll={{ x: true, y: '100%' }}
          onChange={(_pagination, _filters, sorter) => {
            setSortedInfo(sorter as SorterResult<HistoryWorklistType>)
          }}
          expandable={{
            expandIcon: ExpandIcon,
            indentSize: 0,
          }}
          rowClassName={(record) => {
            switch (record.level) {
              case 'main':
                return 'main-row'
              case 'group':
                return 'group-row'
              case 'item':
                return 'item-row'
              default:
                return ''
            }
          }}
        />
      </section>
    </ConfigProvider>
  )
}

export default HistoryWorklistTable

# HistoryWorklistTable

歷史工作清單表格組件，基於 Antd 樹形表格實現。

## 結構說明

### 檔案組織

```
HistoryWorklistTable/
├── index.tsx                              # 主要入口組件
├── HistoryWorklistTable.css              # 樣式文件
├── README.md                             # 說明文件
├── REFACTOR_SUMMARY.md                   # 重構總結
└── hooks/                               # 自定義 hooks
    ├── useHistoryTreeTableColumns.tsx   # 樹形表格欄位定義
    └── useHistoryTreeTableData.ts       # 樹形表格資料處理
```

### 樹形結構

使用 Antd 樹形表格的 `children` 屬性實現三層嵌套：

1. **第一層 (Main)**: `HistoryWorklistType`
   - 顯示研究基本資訊（日期、描述、模態等）
   - 使用 `colSpan: 8` 合併所有欄位，資訊顯示在 Operation 欄位中
   - 其他欄位設為 `colSpan: 0` 被合併

2. **第二層 (Groups)**: `HistoryGroupType & { items: HistoryGroupType[] }`
   - 顯示群組資訊
   - 對應到完整的表格欄位結構

3. **第三層 (Items)**: `HistoryGroupType`
   - 顯示最終的項目資訊
   - 對應到完整的表格欄位結構

### 表格欄位

所有層級都使用相同的欄位結構：
- `Operation`: 操作按鈕
- `Modality`: 模態類型
- `Images`: 影像數量
- `RS Set Label`: RS 集合標籤
- `Series Number`: 系列編號
- `Series Description`: 系列描述
- `Source`: 來源
- `Status`: 狀態

## 樣式配置

### 顏色層級
- **第一層**: `--color-gray_2` 背景，`--color-gray_1` 文字
- **第二層**: `--color-gray_3` 背景，`--color-gray_1` 邊框
- **第三層**: `--color-gray_4` 背景，`--color-gray_1_variants` 邊框

### CSS 類別
- `.history-tree-main-row`: 第一層樣式
- `.history-tree-group-row`: 第二層樣式
- `.history-tree-item-row`: 第三層樣式

## 使用方式

```tsx
<HistoryWorklistTable
  tableLoading={tableLoading}
  setSortedInfo={setHistorySorted}
/>
```

## Props

- `tableLoading: boolean` - 表格載入狀態
- `setSortedInfo: (sorter: SorterResult<HistoryWorklistType>) => void` - 排序回調函數

## 特色功能

1. **樹形展開**: 使用 Antd 原生樹形表格功能
2. **響應式設計**: 適應不同螢幕尺寸
3. **排序功能**: 支援欄位排序
4. **載入狀態**: 顯示載入動畫
5. **操作按鈕**: 每個項目都有對應的操作功能
6. **列合併**: Main 層級自動合併所有欄位顯示

## API 資料

目前使用 `src/components/mock/history-worklist.mock.ts` 中的假資料進行測試。
